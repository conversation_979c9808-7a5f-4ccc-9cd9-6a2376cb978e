"use client";

import { useEffect, useRef } from 'react';

interface AnimatedBackgroundProps {
  variant?: 'matrix' | 'circuit' | 'neural' | 'data-stream';
  intensity?: 'low' | 'medium' | 'high';
  color?: string;
}

export default function AnimatedBackground({ 
  variant = 'matrix', 
  intensity = 'medium',
  color = '#00D4FF'
}: AnimatedBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();

    let animationId: number;

    if (variant === 'matrix') {
      // Matrix rain effect
      const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
      const fontSize = 14;
      const columns = Math.floor(canvas.width / fontSize);
      const drops: number[] = new Array(columns).fill(1);

      const drawMatrix = () => {
        ctx.fillStyle = 'rgba(10, 10, 15, 0.05)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = color;
        ctx.font = `${fontSize}px monospace`;

        for (let i = 0; i < drops.length; i++) {
          const text = chars[Math.floor(Math.random() * chars.length)];
          ctx.fillText(text, i * fontSize, drops[i] * fontSize);

          if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
            drops[i] = 0;
          }
          drops[i]++;
        }

        animationId = requestAnimationFrame(drawMatrix);
      };

      drawMatrix();
    } else if (variant === 'circuit') {
      // Circuit board pattern
      const nodes: { x: number; y: number; connections: number[] }[] = [];
      const nodeCount = intensity === 'low' ? 20 : intensity === 'medium' ? 40 : 60;

      // Generate nodes
      for (let i = 0; i < nodeCount; i++) {
        nodes.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          connections: []
        });
      }

      // Create connections
      nodes.forEach((node, index) => {
        const nearbyNodes = nodes
          .map((otherNode, otherIndex) => ({
            index: otherIndex,
            distance: Math.sqrt(
              Math.pow(node.x - otherNode.x, 2) + Math.pow(node.y - otherNode.y, 2)
            )
          }))
          .filter(({ distance, index: otherIndex }) => distance < 150 && otherIndex !== index)
          .sort((a, b) => a.distance - b.distance)
          .slice(0, 3);

        node.connections = nearbyNodes.map(({ index }) => index);
      });

      let pulseOffset = 0;

      const drawCircuit = () => {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        pulseOffset += 0.02;

        // Draw connections
        ctx.strokeStyle = color;
        ctx.lineWidth = 1;
        nodes.forEach((node, index) => {
          node.connections.forEach(connectionIndex => {
            const connectedNode = nodes[connectionIndex];
            const pulse = Math.sin(pulseOffset + index * 0.1) * 0.5 + 0.5;
            
            ctx.globalAlpha = 0.3 + pulse * 0.4;
            ctx.beginPath();
            ctx.moveTo(node.x, node.y);
            ctx.lineTo(connectedNode.x, connectedNode.y);
            ctx.stroke();
          });
        });

        // Draw nodes
        ctx.fillStyle = color;
        nodes.forEach((node, index) => {
          const pulse = Math.sin(pulseOffset + index * 0.2) * 0.3 + 0.7;
          ctx.globalAlpha = pulse;
          ctx.beginPath();
          ctx.arc(node.x, node.y, 3, 0, Math.PI * 2);
          ctx.fill();
        });

        animationId = requestAnimationFrame(drawCircuit);
      };

      drawCircuit();
    } else if (variant === 'neural') {
      // Neural network visualization
      const neurons: { 
        x: number; 
        y: number; 
        activity: number; 
        connections: { target: number; weight: number }[] 
      }[] = [];
      
      const neuronCount = intensity === 'low' ? 30 : intensity === 'medium' ? 50 : 80;

      // Generate neurons
      for (let i = 0; i < neuronCount; i++) {
        const neuron = {
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          activity: Math.random(),
          connections: []
        };

        // Create random connections
        const connectionCount = Math.floor(Math.random() * 4) + 1;
        for (let j = 0; j < connectionCount; j++) {
          const target = Math.floor(Math.random() * neuronCount);
          if (target !== i) {
            neuron.connections.push({
              target,
              weight: Math.random() * 2 - 1
            });
          }
        }

        neurons.push(neuron);
      }

      const drawNeural = () => {
        ctx.fillStyle = 'rgba(10, 10, 15, 0.1)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Update neuron activities
        neurons.forEach(neuron => {
          neuron.activity = Math.max(0, Math.min(1, neuron.activity + (Math.random() - 0.5) * 0.1));
        });

        // Draw connections
        neurons.forEach((neuron, index) => {
          neuron.connections.forEach(connection => {
            if (connection.target < neurons.length) {
              const target = neurons[connection.target];
              const intensity = neuron.activity * Math.abs(connection.weight);
              
              ctx.strokeStyle = color;
              ctx.globalAlpha = intensity * 0.5;
              ctx.lineWidth = Math.abs(connection.weight) * 2;
              ctx.beginPath();
              ctx.moveTo(neuron.x, neuron.y);
              ctx.lineTo(target.x, target.y);
              ctx.stroke();
            }
          });
        });

        // Draw neurons
        neurons.forEach(neuron => {
          ctx.fillStyle = color;
          ctx.globalAlpha = neuron.activity;
          ctx.beginPath();
          ctx.arc(neuron.x, neuron.y, 4 + neuron.activity * 3, 0, Math.PI * 2);
          ctx.fill();
        });

        animationId = requestAnimationFrame(drawNeural);
      };

      drawNeural();
    } else if (variant === 'data-stream') {
      // Data stream effect
      const streams: { 
        x: number; 
        y: number; 
        speed: number; 
        data: string; 
        opacity: number 
      }[] = [];
      
      const streamCount = intensity === 'low' ? 10 : intensity === 'medium' ? 20 : 30;
      const dataChars = '01ABCDEF';

      // Initialize streams
      for (let i = 0; i < streamCount; i++) {
        streams.push({
          x: Math.random() * canvas.width,
          y: -Math.random() * canvas.height,
          speed: Math.random() * 3 + 1,
          data: Array.from({ length: 20 }, () => 
            dataChars[Math.floor(Math.random() * dataChars.length)]
          ).join(''),
          opacity: Math.random() * 0.7 + 0.3
        });
      }

      const drawDataStream = () => {
        ctx.fillStyle = 'rgba(10, 10, 15, 0.1)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.font = '12px monospace';
        ctx.fillStyle = color;

        streams.forEach(stream => {
          stream.y += stream.speed;

          if (stream.y > canvas.height + 100) {
            stream.y = -100;
            stream.x = Math.random() * canvas.width;
            stream.data = Array.from({ length: 20 }, () => 
              dataChars[Math.floor(Math.random() * dataChars.length)]
            ).join('');
          }

          ctx.globalAlpha = stream.opacity;
          ctx.fillText(stream.data, stream.x, stream.y);
        });

        animationId = requestAnimationFrame(drawDataStream);
      };

      drawDataStream();
    }

    window.addEventListener('resize', resizeCanvas);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [variant, intensity, color]);

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-0"
      style={{ opacity: 0.3 }}
    />
  );
}
