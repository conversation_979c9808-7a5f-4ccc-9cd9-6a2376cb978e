"use client";

import Link from "next/link";
import { Github, Linkedin, Twitter, Mail } from "lucide-react";
import { ScanningText } from "./animated-text";

const navigation = {
  main: [
    { name: "Home", href: "/" },
    { name: "About", href: "/about" },
    { name: "Projects", href: "/projects" },
    { name: "Skills", href: "/skills" },
    { name: "Services", href: "/services" },
    { name: "Contact", href: "/contact" },
  ],
  social: [
    {
      name: "GitHub",
      href: "https://github.com/yourusername",
      icon: Github,
    },
    {
      name: "LinkedIn",
      href: "https://linkedin.com/in/yourusername",
      icon: Linkedin,
    },
    {
      name: "Twitter",
      href: "https://twitter.com/yourusername",
      icon: Twitter,
    },
    {
      name: "Email",
      href: "mailto:<EMAIL>",
      icon: Mail,
    },
  ],
};

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="glass-card border-t border-t-cyan-500/20 mt-20 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-primary to-transparent animate-scan"></div>
      </div>

      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Enhanced Logo and Description */}
          <div className="md:col-span-2">
            <div className="flex items-center mb-4 group">
              <span className="text-2xl font-bold cyber-heading-alt gradient-text transition-all duration-300 group-hover:text-glow">
                CYBER
              </span>
              <span className="text-2xl font-bold cyber-heading-alt text-primary ml-1 transition-all duration-300 group-hover:animate-neon-pulse">
                PORT
              </span>
            </div>
            <p className="cyber-body-alt text-secondary mb-4 max-w-md">
              A passionate full-stack developer and UX/UI designer creating beautiful, functional digital experiences in the digital realm.
            </p>
            <div className="flex space-x-4">
              {navigation.social.map((item, index) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="text-secondary hover:text-primary transition-all duration-300 hover-glow hover-lift group relative"
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <item.icon className="h-6 w-6 group-hover:animate-pulse transition-all duration-300" aria-hidden="true" />
                  {/* Hover effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 -z-10 scale-150"></div>
                </a>
              ))}
            </div>
          </div>

          {/* Enhanced Navigation Links */}
          <div>
            <h3 className="cyber-subheading-alt text-primary mb-4">
              <ScanningText text="NAVIGATION" />
            </h3>
            <ul className="space-y-2">
              {navigation.main.map((item, index) => (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className="cyber-body-alt text-secondary hover:text-primary transition-all duration-300 hover-glow relative group"
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    <span className="relative z-10">{item.name}</span>
                    {/* Hover underline */}
                    <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-secondary group-hover:w-full transition-all duration-300"></div>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Enhanced Contact Info */}
          <div>
            <h3 className="cyber-subheading-alt text-primary mb-4">
              <ScanningText text="CONNECT" />
            </h3>
            <ul className="space-y-2">
              <li>
                <a
                  href="mailto:<EMAIL>"
                  className="cyber-body-alt text-secondary hover:text-primary transition-all duration-300 hover-glow group"
                >
                  <span className="group-hover:animate-pulse"><EMAIL></span>
                </a>
              </li>
              <li>
                <span className="cyber-body-alt text-secondary">
                  Available for freelance work
                </span>
              </li>
              <li>
                <span className="tech-tag inline-block mt-2 animate-pulse">
                  <ScanningText text="STATUS: ONLINE" scanSpeed={1500} />
                </span>
              </li>
            </ul>
          </div>
        </div>

        {/* Enhanced Copyright */}
        <div className="mt-8 pt-8 border-t border-t-cyan-500/20 relative">
          {/* Animated border */}
          <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-primary to-transparent animate-scan"></div>

          <div className="flex flex-col sm:flex-row justify-between items-center">
            <p className="cyber-mono-alt text-tertiary text-sm">
              <ScanningText text={`© ${currentYear} CYBERPORT. ALL RIGHTS RESERVED.`} />
            </p>
            <div className="flex space-x-4 mt-4 sm:mt-0">
              <span className="tech-tag hover-neon transition-all duration-300">v2.0.1</span>
              <span className="tech-tag hover-neon transition-all duration-300">NEXT.JS</span>
              <span className="tech-tag hover-neon transition-all duration-300">CYBERPUNK</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}